# PROTEC Alumni App - Environment Configuration
# Copy this file to .env.local and update with your actual values

# API Configuration
API_BASE_URL=https://api.protec.org
API_VERSION=v1
API_TIMEOUT=30000

# Authentication
JWT_SECRET=your-jwt-secret-key
AUTH_TOKEN_EXPIRY=7d
REFRESH_TOKEN_EXPIRY=30d

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/protec_alumni
REDIS_URL=redis://localhost:6379

# Push Notifications
EXPO_PUSH_TOKEN=your-expo-push-token
FCM_SERVER_KEY=your-fcm-server-key
APNS_KEY_ID=your-apns-key-id
APNS_TEAM_ID=your-apns-team-id

# Payment Gateway (PayFast)
PAYFAST_MERCHANT_ID=your-payfast-merchant-id
PAYFAST_MERCHANT_KEY=your-payfast-merchant-key
PAYFAST_PASSPHRASE=your-payfast-passphrase
PAYFAST_SANDBOX=true

# Email Service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# File Storage
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=af-south-1
AWS_S3_BUCKET=protec-alumni-assets

# Analytics
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
MIXPANEL_TOKEN=your-mixpanel-token

# Error Tracking
SENTRY_DSN=your-sentry-dsn
SENTRY_ORG=protec
SENTRY_PROJECT=alumni-app

# Social Media Integration
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret

# Feature Flags
ENABLE_DONATIONS=true
ENABLE_MESSAGING=true
ENABLE_EVENTS=true
ENABLE_NOTIFICATIONS=true
ENABLE_ANALYTICS=true

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# Security
CORS_ORIGIN=https://protec.org,https://www.protec.org
HELMET_CSP_ENABLED=true
BCRYPT_ROUNDS=12

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Development
NODE_ENV=production
PORT=3000
DEBUG=false
