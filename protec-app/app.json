{"expo": {"name": "PROTEC Alumni", "slug": "protec-alumni-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "protec-alumni", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#012A5B"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.protec.alumni", "buildNumber": "1", "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to upload profile pictures and post images.", "NSPhotoLibraryUsageDescription": "This app accesses your photo library to upload images for posts and profile pictures.", "NSMicrophoneUsageDescription": "This app may use the microphone for voice messages in the future.", "NSContactsUsageDescription": "This app accesses your contacts to help you connect with fellow PROTEC alumni.", "NSLocationWhenInUseUsageDescription": "This app uses your location to show nearby alumni events and meetups.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app uses your location to show nearby alumni events and meetups.", "NSCalendarsUsageDescription": "This app accesses your calendar to help you save alumni events and reminders."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#012A5B"}, "package": "com.protec.alumni", "versionCode": 1, "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.VIBRATE", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.WAKE_LOCK", "android.permission.READ_CONTACTS", "android.permission.WRITE_CONTACTS", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.READ_CALENDAR", "android.permission.WRITE_CALENDAR"], "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-dev-client", ["expo-notifications", {"icon": "./assets/images/notification-icon.png", "color": "#012A5B", "sounds": ["./assets/sounds/notification.wav"]}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with the alumni community.", "cameraPermission": "The app accesses your camera to let you take photos to share with the alumni community."}], ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#012A5B"}], ["expo-contacts", {"contactsPermission": "Allow PROTEC Alumni to access your contacts to help you connect with fellow alumni."}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow PROTEC Alumni to use your location to show nearby alumni events and meetups.", "locationAlwaysPermission": "Allow PROTEC Alumni to use your location to show nearby alumni events and meetups.", "locationWhenInUsePermission": "Allow PROTEC Alumni to use your location to show nearby alumni events and meetups.", "isIosBackgroundLocationEnabled": false, "isAndroidBackgroundLocationEnabled": false}], ["expo-calendar", {"calendarPermission": "Allow PROTEC Alumni to access your calendar to help you save alumni events and reminders."}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "your-eas-project-id"}}}}