import Constants from 'expo-constants';

interface Config {
  API_URL: string;
  WEB_URL: string;
  APP_ENV: 'development' | 'staging' | 'production';
}

const getConfig = (): Config => {
  const isDev = __DEV__;
  
  if (isDev) {
    // Development configuration
    const debuggerHost = Constants.expoConfig?.hostUri?.split(':')[0];
    return {
      API_URL: `http://${debuggerHost || 'localhost'}:3000/api`,
      WEB_URL: `http://${debuggerHost || 'localhost'}:3000`,
      APP_ENV: 'development',
    };
  }
  
  // Production configuration
  return {
    API_URL: 'https://protec.co.za/api',
    WEB_URL: 'https://protec.co.za',
    APP_ENV: 'production',
  };
};

export const config = getConfig();
