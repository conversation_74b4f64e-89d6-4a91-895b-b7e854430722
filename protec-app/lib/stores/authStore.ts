import { create } from 'zustand';
import { 
  getAuthToken, 
  setAuthToken, 
  removeAuthToken, 
  getUserData, 
  setUserData, 
  clearAllAuthData 
} from '../auth/storage';

interface User {
  id: string;
  email: string;
  name: string;
  image?: string;
  role: string;
  isActive: boolean;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  
  // Actions
  login: (token: string, user: User) => Promise<void>;
  logout: () => Promise<void>;
  loadAuthState: () => Promise<void>;
  updateUser: (user: Partial<User>) => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  token: null,
  isLoading: true,
  isAuthenticated: false,

  login: async (token: string, user: User) => {
    try {
      await setAuthToken(token);
      await setUserData(user);
      
      set({
        token,
        user,
        isAuthenticated: true,
        isLoading: false,
      });
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  logout: async () => {
    try {
      await clearAllAuthData();
      
      set({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
      });
    } catch (error) {
      console.error('Logout error:', error);
    }
  },

  loadAuthState: async () => {
    try {
      set({ isLoading: true });
      
      const [token, userData] = await Promise.all([
        getAuthToken(),
        getUserData(),
      ]);

      if (token && userData) {
        set({
          token,
          user: userData,
          isAuthenticated: true,
          isLoading: false,
        });
      } else {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        });
      }
    } catch (error) {
      console.error('Load auth state error:', error);
      set({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
      });
    }
  },

  updateUser: async (updatedUser: Partial<User>) => {
    const { user } = get();
    if (!user) return;

    const newUser = { ...user, ...updatedUser };
    
    try {
      await setUserData(newUser);
      set({ user: newUser });
    } catch (error) {
      console.error('Update user error:', error);
      throw error;
    }
  },
}));
