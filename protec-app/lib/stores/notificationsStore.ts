import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { notificationService, NotificationPreferences, NotificationData } from '@/lib/services/notificationService';

interface NotificationState {
  // Preferences
  preferences: NotificationPreferences;
  isInitialized: boolean;
  pushToken: string | null;
  permissionStatus: string | null;
  
  // Notification history
  notifications: NotificationData[];
  unreadCount: number;
  
  // Actions
  initializeNotifications: () => Promise<void>;
  updatePreferences: (preferences: Partial<NotificationPreferences>) => Promise<void>;
  requestPermissions: () => Promise<boolean>;
  sendTestNotification: (type: NotificationData['type']) => Promise<void>;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  clearNotifications: () => void;
  addNotification: (notification: NotificationData) => void;
  
  // Badge management
  updateBadgeCount: () => Promise<void>;
  clearBadge: () => Promise<void>;
}

const defaultPreferences: NotificationPreferences = {
  messages: true,
  events: true,
  connections: true,
  donations: false,
  posts: false,
  general: true,
  sound: true,
  vibration: true,
};

export const useNotificationsStore = create<NotificationState>()(
  persist(
    (set, get) => ({
      // Initial state
      preferences: defaultPreferences,
      isInitialized: false,
      pushToken: null,
      permissionStatus: null,
      notifications: [],
      unreadCount: 0,

      // Initialize notification service
      initializeNotifications: async () => {
        try {
          const token = await notificationService.initialize();
          const permissions = await notificationService.getPermissions();
          
          set({
            pushToken: token,
            permissionStatus: permissions.status,
            isInitialized: true,
          });

          // Update badge count
          await get().updateBadgeCount();
        } catch (error) {
          console.error('Failed to initialize notifications:', error);
          set({ isInitialized: true }); // Mark as initialized even if failed
        }
      },

      // Update notification preferences
      updatePreferences: async (newPreferences: Partial<NotificationPreferences>) => {
        const currentPreferences = get().preferences;
        const updatedPreferences = { ...currentPreferences, ...newPreferences };
        
        set({ preferences: updatedPreferences });
        
        // TODO: Send preferences to backend
        try {
          // await api.updateNotificationPreferences(updatedPreferences);
        } catch (error) {
          console.error('Failed to update notification preferences:', error);
        }
      },

      // Request notification permissions
      requestPermissions: async () => {
        try {
          const permissions = await notificationService.requestPermissions();
          set({ permissionStatus: permissions.status });
          
          if (permissions.status === 'granted') {
            // Re-initialize to get push token
            await get().initializeNotifications();
            return true;
          }
          
          return false;
        } catch (error) {
          console.error('Failed to request permissions:', error);
          return false;
        }
      },

      // Send test notification
      sendTestNotification: async (type: NotificationData['type']) => {
        const testNotifications: Record<NotificationData['type'], NotificationData> = {
          message: {
            type: 'message',
            id: 'test-message',
            title: 'New Message',
            body: 'You have a new message from John Doe',
            data: { conversationId: 'test-conversation' },
          },
          event: {
            type: 'event',
            id: 'test-event',
            title: 'Event Reminder',
            body: 'Alumni Networking Event starts in 1 hour',
            data: { eventId: 'test-event' },
          },
          connection: {
            type: 'connection',
            id: 'test-connection',
            title: 'New Connection Request',
            body: 'Jane Smith wants to connect with you',
            data: { userId: 'test-user' },
          },
          donation: {
            type: 'donation',
            id: 'test-donation',
            title: 'Donation Campaign',
            body: 'New scholarship fund campaign is now live',
            data: { campaignId: 'test-campaign' },
          },
          post: {
            type: 'post',
            id: 'test-post',
            title: 'New Post',
            body: 'Someone liked your post about career achievements',
            data: { postId: 'test-post' },
          },
          general: {
            type: 'general',
            id: 'test-general',
            title: 'PROTEC Alumni',
            body: 'Welcome to the PROTEC Alumni mobile app!',
            data: {},
          },
        };

        const notification = testNotifications[type];
        await notificationService.sendTestNotification(notification);
        
        // Add to local notifications list
        get().addNotification(notification);
      },

      // Mark notification as read
      markAsRead: (notificationId: string) => {
        const { notifications } = get();
        const updatedNotifications = notifications.map(notification =>
          notification.id === notificationId
            ? { ...notification, read: true }
            : notification
        );
        
        const unreadCount = updatedNotifications.filter(n => !(n as any).read).length;
        
        set({
          notifications: updatedNotifications,
          unreadCount,
        });
        
        get().updateBadgeCount();
      },

      // Mark all notifications as read
      markAllAsRead: () => {
        const { notifications } = get();
        const updatedNotifications = notifications.map(notification => ({
          ...notification,
          read: true,
        }));
        
        set({
          notifications: updatedNotifications,
          unreadCount: 0,
        });
        
        get().clearBadge();
      },

      // Clear all notifications
      clearNotifications: () => {
        set({
          notifications: [],
          unreadCount: 0,
        });
        
        notificationService.clearAllNotifications();
      },

      // Add new notification
      addNotification: (notification: NotificationData) => {
        const { notifications, preferences } = get();
        
        // Check if this type of notification is enabled
        if (!preferences[notification.type]) {
          return;
        }
        
        const newNotification = {
          ...notification,
          read: false,
          timestamp: new Date(),
        };
        
        const updatedNotifications = [newNotification, ...notifications].slice(0, 50); // Keep last 50
        const unreadCount = updatedNotifications.filter(n => !(n as any).read).length;
        
        set({
          notifications: updatedNotifications,
          unreadCount,
        });
        
        get().updateBadgeCount();
      },

      // Update badge count
      updateBadgeCount: async () => {
        const { unreadCount } = get();
        await notificationService.setBadgeCount(unreadCount);
      },

      // Clear badge
      clearBadge: async () => {
        await notificationService.setBadgeCount(0);
      },
    }),
    {
      name: 'notifications-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        preferences: state.preferences,
        notifications: state.notifications,
        unreadCount: state.unreadCount,
      }),
    }
  )
);
