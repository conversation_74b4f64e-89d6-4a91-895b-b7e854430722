{"name": "protec-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "detox test", "test:e2e:build": "detox build", "lint": "expo lint", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@tanstack/react-query": "^5.83.0", "@trpc/client": "^11.4.3", "@trpc/react-query": "^11.4.3", "date-fns": "^4.1.0", "eas-cli": "^16.15.0", "expo": "~53.0.17", "expo-auth-session": "~6.2.1", "expo-blur": "~14.1.5", "expo-calendar": "~14.1.4", "expo-constants": "~17.1.7", "expo-contacts": "~14.2.5", "expo-dev-client": "~5.2.4", "expo-device": "~7.1.1", "expo-file-system": "^18.1.11", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-image-picker": "~16.1.3", "expo-linking": "~7.1.7", "expo-location": "~18.1.6", "expo-notifications": "~0.31.3", "expo-router": "~5.1.3", "expo-secure-store": "^14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "graphql": "^16.11.0", "jest-expo": "~53.0.9", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "~2.24.0", "react-native-mmkv": "^3.3.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "superjson": "^2.2.2", "tslib": "^2.8.1", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.4.3", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "detox": "^20.19.3", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "jest": "^29.7.0", "jest-expo": "~52.0.1", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "overrides": {"react": "19.0.0", "react-dom": "19.0.0"}, "private": true}