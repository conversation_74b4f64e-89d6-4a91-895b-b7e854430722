import * as Contacts from 'expo-contacts';
import { Alert, Platform } from 'react-native';

export interface AlumniContact {
  id: string;
  name: string;
  firstName?: string;
  lastName?: string;
  phoneNumbers?: string[];
  emails?: string[];
  company?: string;
  jobTitle?: string;
  graduationYear?: string;
  isAlumni?: boolean;
}

export class ContactsService {
  /**
   * Request permission to access contacts
   */
  static async requestPermission(): Promise<boolean> {
    try {
      const { status } = await Contacts.requestPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting contacts permission:', error);
      return false;
    }
  }

  /**
   * Check if contacts permission is granted
   */
  static async hasPermission(): Promise<boolean> {
    try {
      const { status } = await Contacts.getPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking contacts permission:', error);
      return false;
    }
  }

  /**
   * Get all contacts from device
   */
  static async getAllContacts(): Promise<AlumniContact[]> {
    try {
      const hasPermission = await this.hasPermission();
      if (!hasPermission) {
        const granted = await this.requestPermission();
        if (!granted) {
          Alert.alert(
            'Permission Required',
            'Please grant contacts permission to connect with fellow alumni.',
            [{ text: 'OK' }]
          );
          return [];
        }
      }

      const { data } = await Contacts.getContactsAsync({
        fields: [
          Contacts.Fields.Name,
          Contacts.Fields.FirstName,
          Contacts.Fields.LastName,
          Contacts.Fields.PhoneNumbers,
          Contacts.Fields.Emails,
          Contacts.Fields.Company,
          Contacts.Fields.JobTitle,
        ],
        sort: Contacts.SortTypes.FirstName,
      });

      return data.map(contact => ({
        id: contact.id || '',
        name: contact.name || `${contact.firstName || ''} ${contact.lastName || ''}`.trim(),
        firstName: contact.firstName,
        lastName: contact.lastName,
        phoneNumbers: contact.phoneNumbers?.map(phone => phone.number).filter((num): num is string => Boolean(num)) || [],
        emails: contact.emails?.map(email => email.email).filter((email): email is string => Boolean(email)) || [],
        company: contact.company,
        jobTitle: contact.jobTitle,
        isAlumni: false, // This would be determined by your app logic
      }));
    } catch (error) {
      console.error('Error fetching contacts:', error);
      Alert.alert('Error', 'Failed to fetch contacts. Please try again.');
      return [];
    }
  }

  /**
   * Search contacts by name or email
   */
  static async searchContacts(query: string): Promise<AlumniContact[]> {
    try {
      const contacts = await this.getAllContacts();
      const lowercaseQuery = query.toLowerCase();
      
      return contacts.filter(contact => 
        contact.name.toLowerCase().includes(lowercaseQuery) ||
        contact.emails?.some(email => email.toLowerCase().includes(lowercaseQuery)) ||
        contact.company?.toLowerCase().includes(lowercaseQuery)
      );
    } catch (error) {
      console.error('Error searching contacts:', error);
      return [];
    }
  }

  /**
   * Add a new contact to device
   */
  static async addContact(contactData: Partial<AlumniContact>): Promise<boolean> {
    try {
      const hasPermission = await this.hasPermission();
      if (!hasPermission) {
        const granted = await this.requestPermission();
        if (!granted) {
          Alert.alert(
            'Permission Required',
            'Please grant contacts permission to add alumni contacts.',
            [{ text: 'OK' }]
          );
          return false;
        }
      }

      const contact: Contacts.Contact = {
        contactType: Contacts.ContactTypes.Person,
        name: `${contactData.firstName || ''} ${contactData.lastName || ''}`.trim(),
        [Contacts.Fields.FirstName]: contactData.firstName || '',
        [Contacts.Fields.LastName]: contactData.lastName || '',
        [Contacts.Fields.Company]: contactData.company || '',
        [Contacts.Fields.JobTitle]: contactData.jobTitle || '',
      };

      if (contactData.phoneNumbers && contactData.phoneNumbers.length > 0) {
        contact[Contacts.Fields.PhoneNumbers] = contactData.phoneNumbers.map(number => ({
          number,
          isPrimary: false,
          digits: number.replace(/\D/g, ''),
          label: 'mobile',
        }));
      }

      if (contactData.emails && contactData.emails.length > 0) {
        contact[Contacts.Fields.Emails] = contactData.emails.map(email => ({
          email,
          isPrimary: false,
          label: 'work',
        }));
      }

      const contactId = await Contacts.addContactAsync(contact);
      return !!contactId;
    } catch (error) {
      console.error('Error adding contact:', error);
      Alert.alert('Error', 'Failed to add contact. Please try again.');
      return false;
    }
  }

  /**
   * Find potential alumni contacts based on email domains or company names
   */
  static async findPotentialAlumni(alumniDomains: string[] = [], alumniCompanies: string[] = []): Promise<AlumniContact[]> {
    try {
      const contacts = await this.getAllContacts();
      
      return contacts.filter(contact => {
        // Check email domains
        const hasAlumniEmail = contact.emails?.some(email => 
          alumniDomains.some(domain => email.toLowerCase().includes(domain.toLowerCase()))
        );
        
        // Check company names
        const hasAlumniCompany = contact.company && 
          alumniCompanies.some(company => 
            contact.company!.toLowerCase().includes(company.toLowerCase())
          );
        
        return hasAlumniEmail || hasAlumniCompany;
      }).map(contact => ({
        ...contact,
        isAlumni: true,
      }));
    } catch (error) {
      console.error('Error finding potential alumni:', error);
      return [];
    }
  }

  /**
   * Export contacts to share with other alumni
   */
  static async exportContactsData(contacts: AlumniContact[]): Promise<string> {
    try {
      const exportData = contacts.map(contact => ({
        name: contact.name,
        email: contact.emails?.[0] || '',
        company: contact.company || '',
        jobTitle: contact.jobTitle || '',
        graduationYear: contact.graduationYear || '',
      }));
      
      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('Error exporting contacts:', error);
      return '';
    }
  }
}

export default ContactsService;
